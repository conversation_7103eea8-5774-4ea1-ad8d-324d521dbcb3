(function(window, undefined) {
    
    window.Asc.plugin.init = function() {
        // 重置编号插件初始化
        console.log("重置编号插件已初始化");
        
        // 自动执行重置编号功能
        this.resetNumbering();
    };

    // 重置编号功能 - 相当于"开始新列表"
    window.Asc.plugin.resetNumbering = function() {
        console.log("执行重置编号");
        
        this.callCommand(function() {
            var oDocument = Api.GetDocument();

            // 获取当前段落 - 使用正确的API方法
            var oParagraph = null;
            try {
                // 方法1：通过选择范围获取段落
                var oRange = oDocument.GetRangeBySelect();
                if (oRange) {
                    oParagraph = oRange.GetParagraph();
                }
            } catch (e) {
                console.log('GetRangeBySelect失败：', e);
            }

            // 方法2：如果方法1失败，通过当前句子定位段落
            if (!oParagraph) {
                try {
                    var currentSentence = oDocument.GetCurrentSentence();
                    if (currentSentence && currentSentence.trim() !== '') {
                        var allParagraphs = oDocument.GetAllParagraphs();
                        var trimmedSentence = currentSentence.trim();

                        for (var i = 0; i < allParagraphs.length; i++) {
                            var testParagraph = allParagraphs[i];
                            var paragraphText = testParagraph.GetText().trim();

                            if (paragraphText.indexOf(trimmedSentence) !== -1) {
                                oParagraph = testParagraph;
                                break;
                            }
                        }
                    }
                } catch (e) {
                    console.log('GetCurrentSentence失败：', e);
                }
            }

            if (oParagraph) {
                console.log('成功获取当前段落');
                console.log('段落文本:', oParagraph.GetText());

                // 获取当前段落的编号信息
                var oNumberingLevel = oParagraph.GetNumbering();
                console.log('编号级别对象:', oNumberingLevel);
                console.log('GetClassType:', oParagraph.GetClassType());
                console.log('GetLevelIndex:', oParagraph.GetLevelIndex());
                console.log('GetNumbering:', oParagraph.GetNumbering());
                console.log('GetParaPr:', oParagraph.GetParaPr());
                console.log('LinkWithStyle:', oParagraph.LinkWithStyle());
                console.log('SetCustomType:', oParagraph.SetCustomType());
                console.log('GetTextPr:', oParagraph.GetTextPr());
                console.log('GetTextPr:', oParagraph.GetTextPr());
                console.log('GetTextPr:', oParagraph.GetTextPr());
                console.log('GetTextPr:', oParagraph.GetTextPr());
                console.log('GetTextPr:', oParagraph.GetTextPr());
                console.log('GetTextPr:', oParagraph.GetTextPr());
                console.log('GetTextPr:', oParagraph.GetTextPr());
                console.log('GetTextPr:', oParagraph.GetTextPr());

                if (oNumberingLevel) {
                    console.log('当前段落有编号，准备重置编号');

                    // 获取当前编号的父级编号对象
                    var oNumbering = oNumberingLevel.GetNumbering();
                    console.log('获取到的编号对象:', oNumbering);

                    // 尝试获取当前编号级别（使用不同的可能方法）
                    var currentLevel = 0; // 默认为0级别
                    try {
                        if (typeof oNumberingLevel.GetLevel === 'function') {
                            currentLevel = oNumberingLevel.GetLevel();
                            console.log('通过GetLevel获取级别:', currentLevel);
                        } else if (typeof oNumberingLevel.GetLvl === 'function') {
                            currentLevel = oNumberingLevel.GetLvl();
                            console.log('通过GetLvl获取级别:', currentLevel);
                        } else {
                            console.log('无法获取级别，使用默认级别0');
                        }
                    } catch (e) {
                        console.log('获取级别失败，使用默认级别0:', e);
                        currentLevel = 0;
                    }

                    // 使用当前编号类型重置
                    if (oNumbering) {
                        console.log('使用当前编号类型重置，级别:', currentLevel);

                        // 获取相同级别的编号
                        var oNewLevel = oNumbering.GetLevel(currentLevel);
                        console.log('获取新的编号级别:', oNewLevel);

                        if (oNewLevel) {
                            // 设置重新开始
                            if (typeof oNewLevel.SetRestart === 'function') {
                                console.log('在新级别上调用SetRestart');
                                oNewLevel.SetRestart(true);
                            }

                            // 设置起始编号为1
                            if (typeof oNewLevel.SetStart === 'function') {
                                console.log('设置起始编号为1');
                                oNewLevel.SetStart(1);
                            }

                            // 重新应用编号到段落
                            console.log('重新应用编号到段落');
                            oParagraph.SetNumbering(oNewLevel);
                            console.log('编号重置完成');
                        } else {
                            console.log('无法获取新编号级别，尝试直接重置');
                            // 直接在当前级别上重置
                            if (typeof oNumberingLevel.SetRestart === 'function') {
                                oNumberingLevel.SetRestart(true);
                                console.log('直接调用SetRestart');
                            }
                            if (typeof oNumberingLevel.SetStart === 'function') {
                                oNumberingLevel.SetStart(1);
                                console.log('直接调用SetStart');
                            }
                        }

                    } else {
                        console.log('无法获取编号对象，尝试直接重置');
                        // 直接在当前级别上重置
                        if (typeof oNumberingLevel.SetRestart === 'function') {
                            oNumberingLevel.SetRestart(true);
                            console.log('直接在当前级别调用SetRestart');
                        }
                        if (typeof oNumberingLevel.SetStart === 'function') {
                            oNumberingLevel.SetStart(1);
                            console.log('直接在当前级别调用SetStart');
                        }
                    }

                    return 1; // 成功
                } else {
                    console.log('当前段落没有编号，创建新编号');
                    // 如果当前段落没有编号，应用默认的编号格式
                    var oNumbering = oDocument.CreateNumbering("numbered");
                    console.log('创建的编号对象:', oNumbering);

                    var oLevel = oNumbering.GetLevel(0);
                    console.log('获取的编号级别:', oLevel);

                    oParagraph.SetNumbering(oLevel);
                    console.log('已应用编号到段落');

                    return 1; // 成功
                }
            } else {
                console.log('错误：无法获取当前段落');
                return -1; // 失败
            }
        }, false, true, (result) => {
            if (result === -1) {
                this.executeMethod('ShowError', ['无法获取当前段落']);
            } else {
                this.executeMethod('ShowNotification', ['重置编号已应用']);
            }
            this.executeCommand('close', '');
        });

    };

    // 插件按钮点击事件
    window.Asc.plugin.button = function(id) {
        if (id == 0) {
            // 主按钮被点击，执行重置编号
            this.resetNumbering();
        }
    };

})(window, undefined);
