(function(window, undefined) {
    
    window.Asc.plugin.init = function() {
        // 重置编号插件初始化
        console.log("重置编号插件已初始化");
        
        // 自动执行重置编号功能
        this.resetNumbering();
    };

    // 重置编号功能 - 相当于"开始新列表"
    window.Asc.plugin.resetNumbering = function() {
        console.log("执行重置编号");
        
        this.callCommand(function() {
            var oDocument = Api.GetDocument();

            // 获取当前段落 - 使用正确的API方法
            var oParagraph = null;
            try {
                // 方法1：通过选择范围获取段落
                var oRange = oDocument.GetRangeBySelect();
                if (oRange) {
                    oParagraph = oRange.GetParagraph();
                }
            } catch (e) {
                console.log('GetRangeBySelect失败：', e);
            }

            // 方法2：如果方法1失败，通过当前句子定位段落
            if (!oParagraph) {
                try {
                    var currentSentence = oDocument.GetCurrentSentence();
                    if (currentSentence && currentSentence.trim() !== '') {
                        var allParagraphs = oDocument.GetAllParagraphs();
                        var trimmedSentence = currentSentence.trim();

                        for (var i = 0; i < allParagraphs.length; i++) {
                            var testParagraph = allParagraphs[i];
                            var paragraphText = testParagraph.GetText().trim();

                            if (paragraphText.indexOf(trimmedSentence) !== -1) {
                                oParagraph = testParagraph;
                                break;
                            }
                        }
                    }
                } catch (e) {
                    console.log('GetCurrentSentence失败：', e);
                }
            }

            if (oParagraph) {
                // 获取当前段落的编号信息
                var oNumberingLevel = oParagraph.GetNumbering();

                if (oNumberingLevel) {
                    // 如果当前段落有编号，创建新的编号并重新开始
                    var oNumbering = oDocument.CreateNumbering("numbered");
                    var oNewLevel = oNumbering.GetLevel(0);
                    oParagraph.SetNumbering(oNewLevel);
                } else {
                    // 如果当前段落没有编号，应用默认的编号格式
                    var oNumbering = oDocument.CreateNumbering("numbered");
                    var oLevel = oNumbering.GetLevel(0);
                    oParagraph.SetNumbering(oLevel);
                }
                return 1; // 成功
            } else {
                console.log('无法获取当前段落');
                return -1; // 失败
            }
        }, false, true, (result) => {
            if (result === -1) {
                this.executeMethod('ShowError', ['无法获取当前段落']);
            } else {
                this.executeMethod('ShowNotification', ['重置编号已应用']);
            }
            this.executeCommand('close', '');
        });
        
        // 显示成功消息并关闭插件
        this.executeMethod('ShowNotification', ['重置编号已应用']);
        this.executeMethod('ClosePlugin');
    };

    // 插件按钮点击事件
    window.Asc.plugin.button = function(id) {
        if (id == 0) {
            // 主按钮被点击，执行重置编号
            this.resetNumbering();
        }
    };

})(window, undefined);
