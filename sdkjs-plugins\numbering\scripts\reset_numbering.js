(function(window, undefined) {
    
    window.Asc.plugin.init = function() {
        // 重置编号插件初始化
        console.log("重置编号插件已初始化");
        
        // 自动执行重置编号功能
        this.resetNumbering();
    };

    // 重置编号功能 - 相当于"开始新列表"
    window.Asc.plugin.resetNumbering = function() {
        console.log("执行重置编号");
        
        this.callCommand(function() {
            var oDocument = Api.GetDocument();

            // 获取当前段落 - 使用正确的API方法
            var oParagraph = null;
            try {
                // 方法1：通过选择范围获取段落
                var oRange = oDocument.GetRangeBySelect();
                if (oRange) {
                    oParagraph = oRange.GetParagraph();
                }
            } catch (e) {
                console.log('GetRangeBySelect失败：', e);
            }

            // 方法2：如果方法1失败，通过当前句子定位段落
            if (!oParagraph) {
                try {
                    var currentSentence = oDocument.GetCurrentSentence();
                    if (currentSentence && currentSentence.trim() !== '') {
                        var allParagraphs = oDocument.GetAllParagraphs();
                        var trimmedSentence = currentSentence.trim();

                        for (var i = 0; i < allParagraphs.length; i++) {
                            var testParagraph = allParagraphs[i];
                            var paragraphText = testParagraph.GetText().trim();

                            if (paragraphText.indexOf(trimmedSentence) !== -1) {
                                oParagraph = testParagraph;
                                break;
                            }
                        }
                    }
                } catch (e) {
                    console.log('GetCurrentSentence失败：', e);
                }
            }

            if (oParagraph) {
                console.log('成功获取当前段落');
                console.log('段落文本:', oParagraph.GetText());

                // 获取当前段落的编号信息
                var oNumberingLevel = oParagraph.GetNumbering();
                console.log('编号级别对象:', oNumberingLevel);

                if (oNumberingLevel) {
                    console.log('当前段落有编号，准备使用SetRestart方法');

                    // 检查SetRestart方法是否存在
                    if (typeof oNumberingLevel.SetRestart === 'function') {
                        console.log('SetRestart方法存在，开始调用');
                        var result = oNumberingLevel.SetRestart(true);
                        console.log('SetRestart调用结果:', result);
                        console.log('SetRestart方法已调用');

                        // 尝试额外的操作来确保重置生效
                        try {
                            // 方法1：尝试设置起始编号为1
                            if (typeof oNumberingLevel.SetStart === 'function') {
                                console.log('尝试设置起始编号为1');
                                oNumberingLevel.SetStart(1);
                                console.log('SetStart(1)已调用');
                            }

                            // 方法2：尝试重新应用编号到段落
                            console.log('尝试重新应用编号');
                            oParagraph.SetNumbering(oNumberingLevel);
                            console.log('重新应用编号完成');

                            // 方法3：尝试刷新文档
                            if (typeof oDocument.Recalculate === 'function') {
                                console.log('尝试重新计算文档');
                                oDocument.Recalculate();
                                console.log('文档重新计算完成');
                            }

                        } catch (e) {
                            console.log('额外操作失败:', e);
                        }

                    } else {
                        console.log('错误：SetRestart方法不存在');
                        console.log('可用方法:', Object.getOwnPropertyNames(oNumberingLevel));

                        // 尝试其他可能的方法
                        if (typeof oNumberingLevel.SetStart === 'function') {
                            console.log('尝试使用SetStart方法');
                            oNumberingLevel.SetStart(1);
                        }
                    }

                    return 1; // 成功
                } else {
                    console.log('当前段落没有编号，创建新编号');
                    // 如果当前段落没有编号，应用默认的编号格式
                    var oNumbering = oDocument.CreateNumbering("numbered");
                    console.log('创建的编号对象:', oNumbering);

                    var oLevel = oNumbering.GetLevel(0);
                    console.log('获取的编号级别:', oLevel);

                    oParagraph.SetNumbering(oLevel);
                    console.log('已应用编号到段落');

                    return 1; // 成功
                }
            } else {
                console.log('错误：无法获取当前段落');
                return -1; // 失败
            }
        }, false, true, (result) => {
            if (result === -1) {
                this.executeMethod('ShowError', ['无法获取当前段落']);
            } else {
                this.executeMethod('ShowNotification', ['重置编号已应用']);
            }
            this.executeCommand('close', '');
        });

    };

    // 插件按钮点击事件
    window.Asc.plugin.button = function(id) {
        if (id == 0) {
            // 主按钮被点击，执行重置编号
            this.resetNumbering();
        }
    };

})(window, undefined);
